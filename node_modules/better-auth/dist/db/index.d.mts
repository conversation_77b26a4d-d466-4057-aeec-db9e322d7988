import { a as Adapter, B as BetterAuthOptions, G as GenericEndpointContext, Q as Where, J as FieldAttribute, a8 as FieldType, K as KyselyDatabaseType } from '../shared/better-auth.BNm-Id9Y.mjs';
export { ak as BetterAuthDbSchema, ab as FieldAttributeConfig, ah as FieldAttributeToObject, ai as InferAdditionalFieldsFromPluginOptions, p as InferFieldsFromOptions, o as InferFieldsFromPlugins, af as InferFieldsInput, ag as InferFieldsInputClient, ae as InferFieldsOutput, ad as InferValueType, aa as InternalAdapter, aj as PluginFieldAttribute, an as accountSchema, am as coreSchema, ac as createFieldAttribute, a9 as createInternalAdapter, as as getAllFields, al as getAuthTables, aB as mergeSchema, az as parseAccountInput, au as parseAccountOutput, ay as parseAdditionalUserInput, aw as parseInputData, ar as parseOutputData, aA as parseSessionInput, av as parseSessionOutput, ax as parseUserInput, at as parseUserOutput, ap as sessionSchema, ao as userSchema, aq as verificationSchema } from '../shared/better-auth.BNm-Id9Y.mjs';
import * as z from 'zod/v4';
import 'kysely';
import 'better-call';
import '../shared/better-auth.DTtXpZYr.mjs';
import '../shared/better-auth.DR57bygo.mjs';
import 'zod/v4/core';
import 'zod';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';

declare function getWithHooks(adapter: Adapter, ctx: {
    options: BetterAuthOptions;
    hooks: Exclude<BetterAuthOptions["databaseHooks"], undefined>[];
}): {
    createWithHooks: <T extends Record<string, any>>(data: T, model: "user" | "session" | "account" | "verification", customCreateFn?: {
        fn: (data: Record<string, any>) => void | Promise<any>;
        executeMainFn?: boolean;
    }, context?: GenericEndpointContext) => Promise<any>;
    updateWithHooks: <T extends Record<string, any>>(data: any, where: Where[], model: "user" | "session" | "account" | "verification", customUpdateFn?: {
        fn: (data: Record<string, any>) => void | Promise<any>;
        executeMainFn?: boolean;
    }, context?: GenericEndpointContext) => Promise<any>;
    updateManyWithHooks: <T extends Record<string, any>>(data: any, where: Where[], model: "user" | "session" | "account" | "verification", customUpdateFn?: {
        fn: (data: Record<string, any>) => void | Promise<any>;
        executeMainFn?: boolean;
    }, context?: GenericEndpointContext) => Promise<any>;
};

declare function toZodSchema<Fields extends Record<string, FieldAttribute | never>, IsClientSide extends boolean>({ fields, isClientSide, }: {
    fields: Fields;
    /**
     * If true, then any fields that have `input: false` will be removed from the schema to prevent user input.
     */
    isClientSide: IsClientSide;
}): z.ZodObject<RemoveNeverProps<{ [key in keyof Fields]: FieldAttributeToSchema<Fields[key], IsClientSide>; }>, z.core.$strip>;
type FieldAttributeToSchema<Field extends FieldAttribute | Record<string, never>, isClientSide extends boolean = false> = Field extends {
    type: any;
} ? GetInput<isClientSide, Field, GetRequired<Field, GetType<Field>>> : Record<string, never>;
type GetType<F extends FieldAttribute> = F extends {
    type: "string";
} ? z.ZodString : F extends {
    type: "number";
} ? z.ZodNumber : F extends {
    type: "boolean";
} ? z.ZodBoolean : F extends {
    type: "date";
} ? z.ZodDate : z.ZodAny;
type GetRequired<F extends FieldAttribute, Schema extends z.core.SomeType> = F extends {
    required: true;
} ? Schema : z.ZodOptional<Schema>;
type GetInput<isClientSide extends boolean, Field extends FieldAttribute, Schema extends z.core.SomeType> = Field extends {
    input: false;
} ? isClientSide extends true ? never : Schema : Schema;
type RemoveNeverProps<T> = {
    [K in keyof T as [T[K]] extends [never] ? never : K]: T[K];
};

declare function getAdapter(options: BetterAuthOptions): Promise<Adapter>;
declare function convertToDB<T extends Record<string, any>>(fields: Record<string, FieldAttribute>, values: T): T;
declare function convertFromDB<T extends Record<string, any>>(fields: Record<string, FieldAttribute>, values: T | null): T | null;

declare function matchType(columnDataType: string, fieldType: FieldType, dbType: KyselyDatabaseType): boolean;
declare function getMigrations(config: BetterAuthOptions): Promise<{
    toBeCreated: {
        table: string;
        fields: Record<string, FieldAttribute>;
        order: number;
    }[];
    toBeAdded: {
        table: string;
        fields: Record<string, FieldAttribute>;
        order: number;
    }[];
    runMigrations: () => Promise<void>;
    compileMigrations: () => Promise<string>;
}>;

declare function getSchema(config: BetterAuthOptions): Record<string, {
    fields: Record<string, FieldAttribute>;
    order: number;
}>;

export { FieldAttribute, FieldType, convertFromDB, convertToDB, getAdapter, getMigrations, getSchema, getWithHooks, matchType, toZodSchema };
export type { FieldAttributeToSchema };
