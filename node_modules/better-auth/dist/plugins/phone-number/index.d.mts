import * as better_call from 'better-call';
import * as z from 'zod/v4';
import { U as User, I as InferOptionSchema } from '../../shared/better-auth.BNm-Id9Y.mjs';
import 'kysely';
import '../../shared/better-auth.DTtXpZYr.mjs';
import '../../shared/better-auth.DR57bygo.mjs';
import 'zod/v4/core';
import 'zod';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';

interface UserWithPhoneNumber extends User {
    phoneNumber: string;
    phoneNumberVerified: boolean;
}
interface PhoneNumberOptions {
    /**
     * Length of the OTP code
     * @default 6
     */
    otpLength?: number;
    /**
     * Send OTP code to the user
     *
     * @param phoneNumber
     * @param code
     * @returns
     */
    sendOTP: (data: {
        phoneNumber: string;
        code: string;
    }, request?: Request) => Promise<void> | void;
    /**
     * a callback to send otp on user requesting to reset their password
     *
     * @param data - contains phone number and code
     * @param request - the request object
     * @returns
     */
    sendPasswordResetOTP?: (data: {
        phoneNumber: string;
        code: string;
    }, request?: Request) => Promise<void> | void;
    /**
     * a callback to send otp on user requesting to reset their password
     *
     * @param data - contains phone number and code
     * @param request - the request object
     * @returns
     * @deprecated Use sendPasswordResetOTP instead. This function will be removed in the next major version.
     */
    sendForgetPasswordOTP?: (data: {
        phoneNumber: string;
        code: string;
    }, request?: Request) => Promise<void> | void;
    /**
     * Expiry time of the OTP code in seconds
     * @default 300
     */
    expiresIn?: number;
    /**
     * Function to validate phone number
     *
     * by default any string is accepted
     */
    phoneNumberValidator?: (phoneNumber: string) => boolean | Promise<boolean>;
    /**
     * Require a phone number verification before signing in
     *
     * @default false
     */
    requireVerification?: boolean;
    /**
     * Callback when phone number is verified
     */
    callbackOnVerification?: (data: {
        phoneNumber: string;
        user: UserWithPhoneNumber;
    }, request?: Request) => void | Promise<void>;
    /**
     * Sign up user after phone number verification
     *
     * the user will be signed up with the temporary email
     * and the phone number will be updated after verification
     */
    signUpOnVerification?: {
        /**
         * When a user signs up, a temporary email will be need to be created
         * to sign up the user. This function should return a temporary email
         * for the user given the phone number
         *
         * @param phoneNumber
         * @returns string (temporary email)
         */
        getTempEmail: (phoneNumber: string) => string;
        /**
         * When a user signs up, a temporary name will be need to be created
         * to sign up the user. This function should return a temporary name
         * for the user given the phone number
         *
         * @param phoneNumber
         * @returns string (temporary name)
         *
         * @default phoneNumber - the phone number will be used as the name
         */
        getTempName?: (phoneNumber: string) => string;
    };
    /**
     * Custom schema for the admin plugin
     */
    schema?: InferOptionSchema<typeof schema>;
    /**
     * Allowed attempts for the OTP code
     * @default 3
     */
    allowedAttempts?: number;
}
declare const phoneNumber: (options?: PhoneNumberOptions) => {
    id: "phone-number";
    endpoints: {
        /**
         * ### Endpoint
         *
         * POST `/sign-in/phone-number`
         *
         * ### API Methods
         *
         * **server:**
         * `auth.api.signInPhoneNumber`
         *
         * **client:**
         * `authClient.signIn.phoneNumber`
         *
         * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-sign-in-phone-number)
         */
        signInPhoneNumber: {
            <AsResponse extends boolean = false, ReturnHeaders extends boolean = false>(inputCtx_0: {
                body: {
                    phoneNumber: string;
                    password: string;
                    rememberMe?: boolean | undefined;
                };
            } & {
                method?: "POST" | undefined;
            } & {
                query?: Record<string, any> | undefined;
            } & {
                params?: Record<string, any>;
            } & {
                request?: Request;
            } & {
                headers?: HeadersInit;
            } & {
                asResponse?: boolean;
                returnHeaders?: boolean;
                use?: better_call.Middleware[];
                path?: string;
            } & {
                asResponse?: AsResponse | undefined;
                returnHeaders?: ReturnHeaders | undefined;
            }): Promise<[AsResponse] extends [true] ? Response : [ReturnHeaders] extends [true] ? {
                headers: Headers;
                response: {
                    token: string;
                    user: UserWithPhoneNumber;
                };
            } : {
                token: string;
                user: UserWithPhoneNumber;
            }>;
            options: {
                method: "POST";
                body: z.ZodObject<{
                    phoneNumber: z.ZodString;
                    password: z.ZodString;
                    rememberMe: z.ZodOptional<z.ZodBoolean>;
                }, z.core.$strip>;
                metadata: {
                    openapi: {
                        summary: string;
                        description: string;
                        responses: {
                            200: {
                                description: string;
                                content: {
                                    "application/json": {
                                        schema: {
                                            type: "object";
                                            properties: {
                                                user: {
                                                    $ref: string;
                                                };
                                                session: {
                                                    $ref: string;
                                                };
                                            };
                                        };
                                    };
                                };
                            };
                            400: {
                                description: string;
                            };
                        };
                    };
                };
            } & {
                use: any[];
            };
            path: "/sign-in/phone-number";
        };
        /**
         * ### Endpoint
         *
         * POST `/phone-number/send-otp`
         *
         * ### API Methods
         *
         * **server:**
         * `auth.api.sendPhoneNumberOTP`
         *
         * **client:**
         * `authClient.phoneNumber.sendOtp`
         *
         * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-phone-number-send-otp)
         */
        sendPhoneNumberOTP: {
            <AsResponse extends boolean = false, ReturnHeaders extends boolean = false>(inputCtx_0: {
                body: {
                    phoneNumber: string;
                };
            } & {
                method?: "POST" | undefined;
            } & {
                query?: Record<string, any> | undefined;
            } & {
                params?: Record<string, any>;
            } & {
                request?: Request;
            } & {
                headers?: HeadersInit;
            } & {
                asResponse?: boolean;
                returnHeaders?: boolean;
                use?: better_call.Middleware[];
                path?: string;
            } & {
                asResponse?: AsResponse | undefined;
                returnHeaders?: ReturnHeaders | undefined;
            }): Promise<[AsResponse] extends [true] ? Response : [ReturnHeaders] extends [true] ? {
                headers: Headers;
                response: {
                    message: string;
                };
            } : {
                message: string;
            }>;
            options: {
                method: "POST";
                body: z.ZodObject<{
                    phoneNumber: z.ZodString;
                }, z.core.$strip>;
                metadata: {
                    openapi: {
                        summary: string;
                        description: string;
                        responses: {
                            200: {
                                description: string;
                                content: {
                                    "application/json": {
                                        schema: {
                                            type: "object";
                                            properties: {
                                                message: {
                                                    type: string;
                                                };
                                            };
                                        };
                                    };
                                };
                            };
                        };
                    };
                };
            } & {
                use: any[];
            };
            path: "/phone-number/send-otp";
        };
        /**
         * ### Endpoint
         *
         * POST `/phone-number/verify`
         *
         * ### API Methods
         *
         * **server:**
         * `auth.api.verifyPhoneNumber`
         *
         * **client:**
         * `authClient.phoneNumber.verify`
         *
         * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-phone-number-verify)
         */
        verifyPhoneNumber: {
            <AsResponse extends boolean = false, ReturnHeaders extends boolean = false>(inputCtx_0: {
                body: {
                    phoneNumber: string;
                    code: string;
                    disableSession?: boolean | undefined;
                    updatePhoneNumber?: boolean | undefined;
                };
            } & {
                method?: "POST" | undefined;
            } & {
                query?: Record<string, any> | undefined;
            } & {
                params?: Record<string, any>;
            } & {
                request?: Request;
            } & {
                headers?: HeadersInit;
            } & {
                asResponse?: boolean;
                returnHeaders?: boolean;
                use?: better_call.Middleware[];
                path?: string;
            } & {
                asResponse?: AsResponse | undefined;
                returnHeaders?: ReturnHeaders | undefined;
            }): Promise<[AsResponse] extends [true] ? Response : [ReturnHeaders] extends [true] ? {
                headers: Headers;
                response: {
                    status: boolean;
                    token: string;
                    user: UserWithPhoneNumber;
                } | {
                    status: boolean;
                    token: null;
                    user: UserWithPhoneNumber;
                };
            } : {
                status: boolean;
                token: string;
                user: UserWithPhoneNumber;
            } | {
                status: boolean;
                token: null;
                user: UserWithPhoneNumber;
            }>;
            options: {
                method: "POST";
                body: z.ZodObject<{
                    phoneNumber: z.ZodString;
                    code: z.ZodString;
                    disableSession: z.ZodOptional<z.ZodBoolean>;
                    updatePhoneNumber: z.ZodOptional<z.ZodBoolean>;
                }, z.core.$strip>;
                metadata: {
                    openapi: {
                        summary: string;
                        description: string;
                        responses: {
                            "200": {
                                description: string;
                                content: {
                                    "application/json": {
                                        schema: {
                                            type: "object";
                                            properties: {
                                                status: {
                                                    type: string;
                                                    description: string;
                                                    enum: boolean[];
                                                };
                                                token: {
                                                    type: string;
                                                    nullable: boolean;
                                                    description: string;
                                                };
                                                user: {
                                                    type: string;
                                                    nullable: boolean;
                                                    properties: {
                                                        id: {
                                                            type: string;
                                                            description: string;
                                                        };
                                                        email: {
                                                            type: string;
                                                            format: string;
                                                            nullable: boolean;
                                                            description: string;
                                                        };
                                                        emailVerified: {
                                                            type: string;
                                                            nullable: boolean;
                                                            description: string;
                                                        };
                                                        name: {
                                                            type: string;
                                                            nullable: boolean;
                                                            description: string;
                                                        };
                                                        image: {
                                                            type: string;
                                                            format: string;
                                                            nullable: boolean;
                                                            description: string;
                                                        };
                                                        phoneNumber: {
                                                            type: string;
                                                            description: string;
                                                        };
                                                        phoneNumberVerified: {
                                                            type: string;
                                                            description: string;
                                                        };
                                                        createdAt: {
                                                            type: string;
                                                            format: string;
                                                            description: string;
                                                        };
                                                        updatedAt: {
                                                            type: string;
                                                            format: string;
                                                            description: string;
                                                        };
                                                    };
                                                    required: string[];
                                                    description: string;
                                                };
                                            };
                                            required: string[];
                                        };
                                    };
                                };
                            };
                            400: {
                                description: string;
                            };
                        };
                    };
                };
            } & {
                use: any[];
            };
            path: "/phone-number/verify";
        };
        /**
         * @deprecated Use requestPasswordResetPhoneNumber instead. This endpoint will be removed in the next major version.
         */
        forgetPasswordPhoneNumber: {
            <AsResponse extends boolean = false, ReturnHeaders extends boolean = false>(inputCtx_0: {
                body: {
                    phoneNumber: string;
                };
            } & {
                method?: "POST" | undefined;
            } & {
                query?: Record<string, any> | undefined;
            } & {
                params?: Record<string, any>;
            } & {
                request?: Request;
            } & {
                headers?: HeadersInit;
            } & {
                asResponse?: boolean;
                returnHeaders?: boolean;
                use?: better_call.Middleware[];
                path?: string;
            } & {
                asResponse?: AsResponse | undefined;
                returnHeaders?: ReturnHeaders | undefined;
            }): Promise<[AsResponse] extends [true] ? Response : [ReturnHeaders] extends [true] ? {
                headers: Headers;
                response: {
                    status: boolean;
                };
            } : {
                status: boolean;
            }>;
            options: {
                method: "POST";
                body: z.ZodObject<{
                    phoneNumber: z.ZodString;
                }, z.core.$strip>;
                metadata: {
                    openapi: {
                        description: string;
                        responses: {
                            "200": {
                                description: string;
                                content: {
                                    "application/json": {
                                        schema: {
                                            type: "object";
                                            properties: {
                                                status: {
                                                    type: string;
                                                    description: string;
                                                    enum: boolean[];
                                                };
                                            };
                                            required: string[];
                                        };
                                    };
                                };
                            };
                        };
                    };
                };
            } & {
                use: any[];
            };
            path: "/phone-number/forget-password";
        };
        requestPasswordResetPhoneNumber: {
            <AsResponse extends boolean = false, ReturnHeaders extends boolean = false>(inputCtx_0: {
                body: {
                    phoneNumber: string;
                };
            } & {
                method?: "POST" | undefined;
            } & {
                query?: Record<string, any> | undefined;
            } & {
                params?: Record<string, any>;
            } & {
                request?: Request;
            } & {
                headers?: HeadersInit;
            } & {
                asResponse?: boolean;
                returnHeaders?: boolean;
                use?: better_call.Middleware[];
                path?: string;
            } & {
                asResponse?: AsResponse | undefined;
                returnHeaders?: ReturnHeaders | undefined;
            }): Promise<[AsResponse] extends [true] ? Response : [ReturnHeaders] extends [true] ? {
                headers: Headers;
                response: {
                    status: boolean;
                };
            } : {
                status: boolean;
            }>;
            options: {
                method: "POST";
                body: z.ZodObject<{
                    phoneNumber: z.ZodString;
                }, z.core.$strip>;
                metadata: {
                    openapi: {
                        description: string;
                        responses: {
                            "200": {
                                description: string;
                                content: {
                                    "application/json": {
                                        schema: {
                                            type: "object";
                                            properties: {
                                                status: {
                                                    type: string;
                                                    description: string;
                                                    enum: boolean[];
                                                };
                                            };
                                            required: string[];
                                        };
                                    };
                                };
                            };
                        };
                    };
                };
            } & {
                use: any[];
            };
            path: "/phone-number/request-password-reset";
        };
        resetPasswordPhoneNumber: {
            <AsResponse extends boolean = false, ReturnHeaders extends boolean = false>(inputCtx_0: {
                body: {
                    otp: string;
                    phoneNumber: string;
                    newPassword: string;
                };
            } & {
                method?: "POST" | undefined;
            } & {
                query?: Record<string, any> | undefined;
            } & {
                params?: Record<string, any>;
            } & {
                request?: Request;
            } & {
                headers?: HeadersInit;
            } & {
                asResponse?: boolean;
                returnHeaders?: boolean;
                use?: better_call.Middleware[];
                path?: string;
            } & {
                asResponse?: AsResponse | undefined;
                returnHeaders?: ReturnHeaders | undefined;
            }): Promise<[AsResponse] extends [true] ? Response : [ReturnHeaders] extends [true] ? {
                headers: Headers;
                response: {
                    status: boolean;
                };
            } : {
                status: boolean;
            }>;
            options: {
                method: "POST";
                body: z.ZodObject<{
                    otp: z.ZodString;
                    phoneNumber: z.ZodString;
                    newPassword: z.ZodString;
                }, z.core.$strip>;
                metadata: {
                    openapi: {
                        description: string;
                        responses: {
                            "200": {
                                description: string;
                                content: {
                                    "application/json": {
                                        schema: {
                                            type: "object";
                                            properties: {
                                                status: {
                                                    type: string;
                                                    description: string;
                                                    enum: boolean[];
                                                };
                                            };
                                            required: string[];
                                        };
                                    };
                                };
                            };
                        };
                    };
                };
            } & {
                use: any[];
            };
            path: "/phone-number/reset-password";
        };
    };
    schema: {
        user: {
            fields: {
                phoneNumber: {
                    type: "string";
                    required: false;
                    unique: true;
                    sortable: true;
                    returned: true;
                };
                phoneNumberVerified: {
                    type: "boolean";
                    required: false;
                    returned: true;
                    input: false;
                };
            };
        };
    };
    rateLimit: {
        pathMatcher(path: string): boolean;
        window: number;
        max: number;
    }[];
    $ERROR_CODES: {
        readonly INVALID_PHONE_NUMBER: "Invalid phone number";
        readonly PHONE_NUMBER_EXIST: "Phone number already exists";
        readonly INVALID_PHONE_NUMBER_OR_PASSWORD: "Invalid phone number or password";
        readonly UNEXPECTED_ERROR: "Unexpected error";
        readonly OTP_NOT_FOUND: "OTP not found";
        readonly OTP_EXPIRED: "OTP expired";
        readonly INVALID_OTP: "Invalid OTP";
        readonly PHONE_NUMBER_NOT_VERIFIED: "Phone number not verified";
    };
};
declare const schema: {
    user: {
        fields: {
            phoneNumber: {
                type: "string";
                required: false;
                unique: true;
                sortable: true;
                returned: true;
            };
            phoneNumberVerified: {
                type: "boolean";
                required: false;
                returned: true;
                input: false;
            };
        };
    };
};

export { phoneNumber };
export type { PhoneNumberOptions, UserWithPhoneNumber };
