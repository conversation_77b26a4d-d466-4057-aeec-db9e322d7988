import * as better_call from 'better-call';
import { OpenAPIParameter, OpenAPISchemaType } from 'better-call';
import { h as AuthContext, B as BetterAuthOptions } from '../../shared/better-auth.BNm-Id9Y.mjs';
import { L as LiteralString } from '../../shared/better-auth.DTtXpZYr.mjs';
import 'kysely';
import 'zod/v4';
import '../../shared/better-auth.DR57bygo.mjs';
import 'zod/v4/core';
import 'zod';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';

interface Path {
    get?: {
        tags?: string[];
        operationId?: string;
        description?: string;
        security?: [{
            bearerAuth: string[];
        }];
        parameters?: OpenAPIParameter[];
        responses?: {
            [key in string]: {
                description?: string;
                content: {
                    "application/json": {
                        schema: {
                            type?: OpenAPISchemaType;
                            properties?: Record<string, any>;
                            required?: string[];
                            $ref?: string;
                        };
                    };
                };
            };
        };
    };
    post?: {
        tags?: string[];
        operationId?: string;
        description?: string;
        security?: [{
            bearerAuth: string[];
        }];
        parameters?: OpenAPIParameter[];
        requestBody?: {
            content: {
                "application/json": {
                    schema: {
                        type?: OpenAPISchemaType;
                        properties?: Record<string, any>;
                        required?: string[];
                        $ref?: string;
                    };
                };
            };
        };
        responses?: {
            [key in string]: {
                description?: string;
                content: {
                    "application/json": {
                        schema: {
                            type?: OpenAPISchemaType;
                            properties?: Record<string, any>;
                            required?: string[];
                            $ref?: string;
                        };
                    };
                };
            };
        };
    };
}
declare function generator(ctx: AuthContext, options: BetterAuthOptions): Promise<{
    openapi: string;
    info: {
        title: string;
        description: string;
        version: string;
    };
    components: {
        securitySchemes: {
            apiKeyCookie: {
                type: string;
                in: string;
                name: string;
                description: string;
            };
            bearerAuth: {
                type: string;
                scheme: string;
                description: string;
            };
        };
        schemas: {};
    };
    security: {
        apiKeyCookie: never[];
        bearerAuth: never[];
    }[];
    servers: {
        url: string;
    }[];
    tags: {
        name: string;
        description: string;
    }[];
    paths: Record<string, Path>;
}>;

type ScalarTheme = "alternate" | "default" | "moon" | "purple" | "solarized" | "bluePlanet" | "saturn" | "kepler" | "mars" | "deepSpace" | "laserwave" | "none";
interface OpenAPIOptions {
    /**
     * The path to the OpenAPI reference page
     *
     * keep in mind that this path will be appended to the base URL `/api/auth` path
     * by default, so if you set this to `/reference`, the full path will be `/api/auth/reference`
     *
     * @default "/reference"
     */
    path?: LiteralString;
    /**
     * Disable the default reference page that is generated by Scalar
     *
     * @default false
     */
    disableDefaultReference?: boolean;
    /**
     * Theme of the OpenAPI reference page.
     *
     * @default "default"
     */
    theme?: ScalarTheme;
}
declare const openAPI: <O extends OpenAPIOptions>(options?: O) => {
    id: "open-api";
    endpoints: {
        generateOpenAPISchema: {
            <AsResponse extends boolean = false, ReturnHeaders extends boolean = false>(inputCtx_0?: ({
                body?: undefined;
            } & {
                method?: "GET" | undefined;
            } & {
                query?: Record<string, any> | undefined;
            } & {
                params?: Record<string, any>;
            } & {
                request?: Request;
            } & {
                headers?: HeadersInit;
            } & {
                asResponse?: boolean;
                returnHeaders?: boolean;
                use?: better_call.Middleware[];
                path?: string;
            } & {
                asResponse?: AsResponse | undefined;
                returnHeaders?: ReturnHeaders | undefined;
            }) | undefined): Promise<[AsResponse] extends [true] ? Response : [ReturnHeaders] extends [true] ? {
                headers: Headers;
                response: {
                    openapi: string;
                    info: {
                        title: string;
                        description: string;
                        version: string;
                    };
                    components: {
                        securitySchemes: {
                            apiKeyCookie: {
                                type: string;
                                in: string;
                                name: string;
                                description: string;
                            };
                            bearerAuth: {
                                type: string;
                                scheme: string;
                                description: string;
                            };
                        };
                        schemas: {};
                    };
                    security: {
                        apiKeyCookie: never[];
                        bearerAuth: never[];
                    }[];
                    servers: {
                        url: string;
                    }[];
                    tags: {
                        name: string;
                        description: string;
                    }[];
                    paths: Record<string, Path>;
                };
            } : {
                openapi: string;
                info: {
                    title: string;
                    description: string;
                    version: string;
                };
                components: {
                    securitySchemes: {
                        apiKeyCookie: {
                            type: string;
                            in: string;
                            name: string;
                            description: string;
                        };
                        bearerAuth: {
                            type: string;
                            scheme: string;
                            description: string;
                        };
                    };
                    schemas: {};
                };
                security: {
                    apiKeyCookie: never[];
                    bearerAuth: never[];
                }[];
                servers: {
                    url: string;
                }[];
                tags: {
                    name: string;
                    description: string;
                }[];
                paths: Record<string, Path>;
            }>;
            options: {
                method: "GET";
            } & {
                use: any[];
            };
            path: "/open-api/generate-schema";
        };
        openAPIReference: {
            <AsResponse extends boolean = false, ReturnHeaders extends boolean = false>(inputCtx_0?: ({
                body?: undefined;
            } & {
                method?: "GET" | undefined;
            } & {
                query?: Record<string, any> | undefined;
            } & {
                params?: Record<string, any>;
            } & {
                request?: Request;
            } & {
                headers?: HeadersInit;
            } & {
                asResponse?: boolean;
                returnHeaders?: boolean;
                use?: better_call.Middleware[];
                path?: string;
            } & {
                asResponse?: AsResponse | undefined;
                returnHeaders?: ReturnHeaders | undefined;
            }) | undefined): Promise<[AsResponse] extends [true] ? Response : [ReturnHeaders] extends [true] ? {
                headers: Headers;
                response: Response;
            } : Response>;
            options: {
                method: "GET";
                metadata: {
                    isAction: boolean;
                };
            } & {
                use: any[];
            };
            path: "/reference";
        };
    };
};

export { generator, openAPI };
export type { OpenAPIOptions, Path };
