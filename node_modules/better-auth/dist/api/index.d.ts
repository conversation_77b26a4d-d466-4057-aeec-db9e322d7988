import '../shared/better-auth.B5FL4Q2B.js';
export { z as AuthEndpoint, D as AuthMiddleware, bp as accountInfo, aS as callbackOAuth, bg as changeEmail, bc as changePassword, aN as checkEndpointConflicts, y as createAuthEndpoint, x as createAuthMiddleware, b7 as createEmailVerificationToken, be as deleteUser, bf as deleteUserCallback, bh as error, b3 as forgetPassword, b5 as forgetPasswordCallback, aY as freshSessionMiddleware, bn as getAccessToken, aO as getEndpoints, aU as getSession, aV as getSessionFromCtx, aT as getSessionQuerySchema, bl as linkSocialAccount, aZ as listSessions, bk as listUserAccounts, bi as ok, w as optionsMiddleware, br as originCheck, bq as originCheckMiddleware, bo as refreshToken, aX as requestOnlySessionMiddleware, b2 as requestPasswordReset, b4 as requestPasswordResetCallback, b6 as resetPassword, b0 as revokeOtherSessions, a_ as revokeSession, a$ as revokeSessions, aP as router, b9 as sendVerificationEmail, b8 as sendVerificationEmailFn, aW as sessionMiddleware, bd as setPassword, aR as signInEmail, aQ as signInSocial, b1 as signOut, bj as signUpEmail, bm as unlinkAccount, bb as updateUser, ba as verifyEmail } from '../shared/better-auth.tThsKLej.js';
export { APIError } from 'better-call';
import '../shared/better-auth.DTtXpZYr.js';
import 'zod/v4/core';
import 'zod';
import 'zod/v4';
import 'kysely';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';
