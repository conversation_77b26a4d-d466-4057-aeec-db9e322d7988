import { OperationNodeTransformer } from '../../operation-node/operation-node-transformer.js';
import { PrimitiveValueListNode } from '../../operation-node/primitive-value-list-node.js';
import { ValueNode } from '../../operation-node/value-node.js';
/**
 * Transforms all ValueNodes to immediate.
 *
 * WARNING! This should never be part of the public API. Users should never use this.
 * This is an internal helper.
 *
 * @internal
 */
export declare class ImmediateValueTransformer extends OperationNodeTransformer {
    transformPrimitiveValueList(node: PrimitiveValueListNode): PrimitiveValueListNode;
    transformValue(node: ValueNode): ValueNode;
}
