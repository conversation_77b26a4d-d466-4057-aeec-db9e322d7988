import { JoinType } from '../operation-node/join-node.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../query-builder/join-builder.js';
import { OverBuilder } from '../query-builder/over-builder.js';
import { SelectQueryBuilder } from '../query-builder/select-query-builder.js';
import { QueryCreator } from '../query-creator.js';
import { TableExpression } from './table-parser.js';
export declare function createSelectQueryBuilder(): SelectQueryBuilder<any, any, any>;
export declare function createQueryCreator(): QueryCreator<any>;
export declare function createJoinBuilder(joinType: JoinType, table: TableExpression<any, any>): JoinBuilder<any, any>;
export declare function createOverBuilder(): OverBuilder<any, any>;
