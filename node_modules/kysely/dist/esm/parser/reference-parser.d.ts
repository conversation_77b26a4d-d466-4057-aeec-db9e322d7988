import { AliasNode } from '../operation-node/alias-node.js';
import { ColumnNode } from '../operation-node/column-node.js';
import { ReferenceNode } from '../operation-node/reference-node.js';
import { AnyColumn, AnyColumnWithTable, ExtractColumnType } from '../util/type-utils.js';
import { SelectQueryBuilderExpression } from '../query-builder/select-query-builder-expression.js';
import { ExpressionOrFactory } from './expression-parser.js';
import { DynamicReferenceBuilder } from '../dynamic/dynamic-reference-builder.js';
import { SelectType } from '../util/column-type.js';
import { OperationNode } from '../operation-node/operation-node.js';
import { Expression } from '../expression/expression.js';
import { SimpleReferenceExpressionNode } from '../operation-node/simple-reference-expression-node.js';
import { OrderByDirection } from './order-by-parser.js';
import { JSONOperatorWith$ } from '../operation-node/operator-node.js';
import { JSONReferenceNode } from '../operation-node/json-reference-node.js';
export type StringReference<DB, TB extends keyof DB> = AnyColumn<DB, TB> | AnyColumnWithTable<DB, TB>;
export type SimpleReferenceExpression<DB, TB extends keyof DB> = StringReference<DB, TB> | DynamicReferenceBuilder<any>;
export type ReferenceExpression<DB, TB extends keyof DB> = SimpleReferenceExpression<DB, TB> | ExpressionOrFactory<DB, TB, any>;
export type ReferenceExpressionOrList<DB, TB extends keyof DB> = ReferenceExpression<DB, TB> | ReadonlyArray<ReferenceExpression<DB, TB>>;
export type ExtractTypeFromReferenceExpression<DB, TB extends keyof DB, RE, DV = unknown> = SelectType<ExtractRawTypeFromReferenceExpression<DB, TB, RE, DV>>;
export type ExtractRawTypeFromReferenceExpression<DB, TB extends keyof DB, RE, DV = unknown> = RE extends string ? ExtractTypeFromStringReference<DB, TB, RE> : RE extends SelectQueryBuilderExpression<infer O> ? O[keyof O] | null : RE extends (qb: any) => SelectQueryBuilderExpression<infer O> ? O[keyof O] | null : RE extends Expression<infer O> ? O : RE extends (qb: any) => Expression<infer O> ? O : DV;
export type ExtractTypeFromStringReference<DB, TB extends keyof DB, RE extends string, DV = unknown> = RE extends `${infer SC}.${infer T}.${infer C}` ? `${SC}.${T}` extends TB ? C extends keyof DB[`${SC}.${T}`] ? DB[`${SC}.${T}`][C] : never : never : RE extends `${infer T}.${infer C}` ? T extends TB ? C extends keyof DB[T] ? DB[T][C] : never : never : RE extends AnyColumn<DB, TB> ? ExtractColumnType<DB, TB, RE> : DV;
export type OrderedColumnName<C extends string> = C extends `${string} ${infer O}` ? O extends OrderByDirection ? C : never : C;
export type ExtractColumnNameFromOrderedColumnName<C extends string> = C extends `${infer CL} ${infer O}` ? O extends OrderByDirection ? CL : never : C;
export declare function parseSimpleReferenceExpression(exp: SimpleReferenceExpression<any, any>): SimpleReferenceExpressionNode;
export declare function parseReferenceExpressionOrList(arg: ReferenceExpressionOrList<any, any>): OperationNode[];
export declare function parseReferenceExpression(exp: ReferenceExpression<any, any>): OperationNode;
export declare function parseJSONReference(ref: string, op: JSONOperatorWith$): JSONReferenceNode;
export declare function parseStringReference(ref: string): ReferenceNode;
export declare function parseAliasedStringReference(ref: string): SimpleReferenceExpressionNode | AliasNode;
export declare function parseColumnName(column: AnyColumn<any, any>): ColumnNode;
export declare function parseOrderedColumnName(column: string): OperationNode;
