import { AliasNode } from '../operation-node/alias-node.js';
import { AndNode } from '../operation-node/and-node.js';
import { CheckConstraintNode } from '../operation-node/check-constraint-node.js';
import { AddColumnNode } from '../operation-node/add-column-node.js';
import { ColumnUpdateNode } from '../operation-node/column-update-node.js';
import { CreateIndexNode } from '../operation-node/create-index-node.js';
import { CreateTableNode } from '../operation-node/create-table-node.js';
import { DataTypeNode } from '../operation-node/data-type-node.js';
import { DeleteQueryNode } from '../operation-node/delete-query-node.js';
import { DropIndexNode } from '../operation-node/drop-index-node.js';
import { DropTableNode } from '../operation-node/drop-table-node.js';
import { FromNode } from '../operation-node/from-node.js';
import { GroupByItemNode } from '../operation-node/group-by-item-node.js';
import { GroupByNode } from '../operation-node/group-by-node.js';
import { IdentifierNode } from '../operation-node/identifier-node.js';
import { InsertQueryNode } from '../operation-node/insert-query-node.js';
import { JoinNode } from '../operation-node/join-node.js';
import { LimitNode } from '../operation-node/limit-node.js';
import { ListNode } from '../operation-node/list-node.js';
import { OffsetNode } from '../operation-node/offset-node.js';
import { OnConflictNode } from '../operation-node/on-conflict-node.js';
import { OperationNode } from '../operation-node/operation-node.js';
import { OperationNodeVisitor } from '../operation-node/operation-node-visitor.js';
import { OperatorNode } from '../operation-node/operator-node.js';
import { OrNode } from '../operation-node/or-node.js';
import { OrderByItemNode } from '../operation-node/order-by-item-node.js';
import { OrderByNode } from '../operation-node/order-by-node.js';
import { ParensNode } from '../operation-node/parens-node.js';
import { PrimitiveValueListNode } from '../operation-node/primitive-value-list-node.js';
import { RawNode } from '../operation-node/raw-node.js';
import { ReferenceNode } from '../operation-node/reference-node.js';
import { ReferencesNode } from '../operation-node/references-node.js';
import { ReturningNode } from '../operation-node/returning-node.js';
import { SelectAllNode } from '../operation-node/select-all-node.js';
import { SelectQueryNode } from '../operation-node/select-query-node.js';
import { SelectionNode } from '../operation-node/selection-node.js';
import { TableNode } from '../operation-node/table-node.js';
import { PrimaryKeyConstraintNode } from '../operation-node/primary-key-constraint-node.js';
import { UniqueConstraintNode } from '../operation-node/unique-constraint-node.js';
import { UpdateQueryNode } from '../operation-node/update-query-node.js';
import { ValueListNode } from '../operation-node/value-list-node.js';
import { ValueNode } from '../operation-node/value-node.js';
import { WhereNode } from '../operation-node/where-node.js';
import { CommonTableExpressionNode } from '../operation-node/common-table-expression-node.js';
import { WithNode } from '../operation-node/with-node.js';
import { CompiledQuery } from './compiled-query.js';
import { RootOperationNode, QueryCompiler } from './query-compiler.js';
import { HavingNode } from '../operation-node/having-node.js';
import { CreateSchemaNode } from '../operation-node/create-schema-node.js';
import { DropSchemaNode } from '../operation-node/drop-schema-node.js';
import { AlterTableColumnAlterationNode, AlterTableNode } from '../operation-node/alter-table-node.js';
import { DropColumnNode } from '../operation-node/drop-column-node.js';
import { RenameColumnNode } from '../operation-node/rename-column-node.js';
import { AlterColumnNode } from '../operation-node/alter-column-node.js';
import { AddConstraintNode } from '../operation-node/add-constraint-node.js';
import { DropConstraintNode } from '../operation-node/drop-constraint-node.js';
import { ForeignKeyConstraintNode } from '../operation-node/foreign-key-constraint-node.js';
import { ColumnDefinitionNode } from '../operation-node/column-definition-node.js';
import { ModifyColumnNode } from '../operation-node/modify-column-node.js';
import { OnDuplicateKeyNode } from '../operation-node/on-duplicate-key-node.js';
import { ColumnNode } from '../operation-node/column-node.js';
import { CreateViewNode } from '../operation-node/create-view-node.js';
import { DropViewNode } from '../operation-node/drop-view-node.js';
import { GeneratedNode } from '../operation-node/generated-node.js';
import { DefaultValueNode } from '../operation-node/default-value-node.js';
import { OnNode } from '../operation-node/on-node.js';
import { ValuesNode } from '../operation-node/values-node.js';
import { CommonTableExpressionNameNode } from '../operation-node/common-table-expression-name-node.js';
import { SelectModifierNode } from '../operation-node/select-modifier-node.js';
import { CreateTypeNode } from '../operation-node/create-type-node.js';
import { DropTypeNode } from '../operation-node/drop-type-node.js';
import { ExplainNode } from '../operation-node/explain-node.js';
import { SchemableIdentifierNode } from '../operation-node/schemable-identifier-node.js';
import { DefaultInsertValueNode } from '../operation-node/default-insert-value-node.js';
import { AggregateFunctionNode } from '../operation-node/aggregate-function-node.js';
import { OverNode } from '../operation-node/over-node.js';
import { PartitionByNode } from '../operation-node/partition-by-node.js';
import { PartitionByItemNode } from '../operation-node/partition-by-item-node.js';
import { SetOperationNode } from '../operation-node/set-operation-node.js';
import { BinaryOperationNode } from '../operation-node/binary-operation-node.js';
import { UnaryOperationNode } from '../operation-node/unary-operation-node.js';
import { UsingNode } from '../operation-node/using-node.js';
import { FunctionNode } from '../operation-node/function-node.js';
import { CaseNode } from '../operation-node/case-node.js';
import { WhenNode } from '../operation-node/when-node.js';
import { JSONReferenceNode } from '../operation-node/json-reference-node.js';
import { JSONPathNode } from '../operation-node/json-path-node.js';
import { JSONPathLegNode } from '../operation-node/json-path-leg-node.js';
import { JSONOperatorChainNode } from '../operation-node/json-operator-chain-node.js';
import { TupleNode } from '../operation-node/tuple-node.js';
import { MergeQueryNode } from '../operation-node/merge-query-node.js';
import { MatchedNode } from '../operation-node/matched-node.js';
import { AddIndexNode } from '../operation-node/add-index-node.js';
import { CastNode } from '../operation-node/cast-node.js';
import { FetchNode } from '../operation-node/fetch-node.js';
import { TopNode } from '../operation-node/top-node.js';
import { OutputNode } from '../operation-node/output-node.js';
import { RefreshMaterializedViewNode } from '../operation-node/refresh-materialized-view-node.js';
import { OrActionNode } from '../operation-node/or-action-node.js';
import { CollateNode } from '../operation-node/collate-node.js';
import { QueryId } from '../util/query-id.js';
import { RenameConstraintNode } from '../operation-node/rename-constraint-node.js';
export declare class DefaultQueryCompiler extends OperationNodeVisitor implements QueryCompiler {
    #private;
    protected get numParameters(): number;
    compileQuery(node: RootOperationNode, queryId: QueryId): CompiledQuery;
    protected getSql(): string;
    protected visitSelectQuery(node: SelectQueryNode): void;
    protected visitFrom(node: FromNode): void;
    protected visitSelection(node: SelectionNode): void;
    protected visitColumn(node: ColumnNode): void;
    protected compileDistinctOn(expressions: ReadonlyArray<OperationNode>): void;
    protected compileList(nodes: ReadonlyArray<OperationNode>, separator?: string): void;
    protected visitWhere(node: WhereNode): void;
    protected visitHaving(node: HavingNode): void;
    protected visitInsertQuery(node: InsertQueryNode): void;
    protected visitValues(node: ValuesNode): void;
    protected visitDeleteQuery(node: DeleteQueryNode): void;
    protected visitReturning(node: ReturningNode): void;
    protected visitAlias(node: AliasNode): void;
    protected visitReference(node: ReferenceNode): void;
    protected visitSelectAll(_: SelectAllNode): void;
    protected visitIdentifier(node: IdentifierNode): void;
    protected compileUnwrappedIdentifier(node: IdentifierNode): void;
    protected visitAnd(node: AndNode): void;
    protected visitOr(node: OrNode): void;
    protected visitValue(node: ValueNode): void;
    protected visitValueList(node: ValueListNode): void;
    protected visitTuple(node: TupleNode): void;
    protected visitPrimitiveValueList(node: PrimitiveValueListNode): void;
    protected visitParens(node: ParensNode): void;
    protected visitJoin(node: JoinNode): void;
    protected visitOn(node: OnNode): void;
    protected visitRaw(node: RawNode): void;
    protected visitOperator(node: OperatorNode): void;
    protected visitTable(node: TableNode): void;
    protected visitSchemableIdentifier(node: SchemableIdentifierNode): void;
    protected visitCreateTable(node: CreateTableNode): void;
    protected visitColumnDefinition(node: ColumnDefinitionNode): void;
    protected getAutoIncrement(): string;
    protected visitReferences(node: ReferencesNode): void;
    protected visitDropTable(node: DropTableNode): void;
    protected visitDataType(node: DataTypeNode): void;
    protected visitOrderBy(node: OrderByNode): void;
    protected visitOrderByItem(node: OrderByItemNode): void;
    protected visitGroupBy(node: GroupByNode): void;
    protected visitGroupByItem(node: GroupByItemNode): void;
    protected visitUpdateQuery(node: UpdateQueryNode): void;
    protected visitColumnUpdate(node: ColumnUpdateNode): void;
    protected visitLimit(node: LimitNode): void;
    protected visitOffset(node: OffsetNode): void;
    protected visitOnConflict(node: OnConflictNode): void;
    protected visitOnDuplicateKey(node: OnDuplicateKeyNode): void;
    protected visitCreateIndex(node: CreateIndexNode): void;
    protected visitDropIndex(node: DropIndexNode): void;
    protected visitCreateSchema(node: CreateSchemaNode): void;
    protected visitDropSchema(node: DropSchemaNode): void;
    protected visitPrimaryKeyConstraint(node: PrimaryKeyConstraintNode): void;
    protected buildDeferrable(node: {
        deferrable?: boolean;
        initiallyDeferred?: boolean;
    }): void;
    protected visitUniqueConstraint(node: UniqueConstraintNode): void;
    protected visitCheckConstraint(node: CheckConstraintNode): void;
    protected visitForeignKeyConstraint(node: ForeignKeyConstraintNode): void;
    protected visitList(node: ListNode): void;
    protected visitWith(node: WithNode): void;
    protected visitCommonTableExpression(node: CommonTableExpressionNode): void;
    protected visitCommonTableExpressionName(node: CommonTableExpressionNameNode): void;
    protected visitAlterTable(node: AlterTableNode): void;
    protected visitAddColumn(node: AddColumnNode): void;
    protected visitRenameColumn(node: RenameColumnNode): void;
    protected visitDropColumn(node: DropColumnNode): void;
    protected visitAlterColumn(node: AlterColumnNode): void;
    protected visitModifyColumn(node: ModifyColumnNode): void;
    protected visitAddConstraint(node: AddConstraintNode): void;
    protected visitDropConstraint(node: DropConstraintNode): void;
    protected visitRenameConstraint(node: RenameConstraintNode): void;
    protected visitSetOperation(node: SetOperationNode): void;
    protected visitCreateView(node: CreateViewNode): void;
    protected visitRefreshMaterializedView(node: RefreshMaterializedViewNode): void;
    protected visitDropView(node: DropViewNode): void;
    protected visitGenerated(node: GeneratedNode): void;
    protected visitDefaultValue(node: DefaultValueNode): void;
    protected visitSelectModifier(node: SelectModifierNode): void;
    protected visitCreateType(node: CreateTypeNode): void;
    protected visitDropType(node: DropTypeNode): void;
    protected visitExplain(node: ExplainNode): void;
    protected visitDefaultInsertValue(_: DefaultInsertValueNode): void;
    protected visitAggregateFunction(node: AggregateFunctionNode): void;
    protected visitOver(node: OverNode): void;
    protected visitPartitionBy(node: PartitionByNode): void;
    protected visitPartitionByItem(node: PartitionByItemNode): void;
    protected visitBinaryOperation(node: BinaryOperationNode): void;
    protected visitUnaryOperation(node: UnaryOperationNode): void;
    protected isMinusOperator(node: OperationNode): node is OperatorNode;
    protected visitUsing(node: UsingNode): void;
    protected visitFunction(node: FunctionNode): void;
    protected visitCase(node: CaseNode): void;
    protected visitWhen(node: WhenNode): void;
    protected visitJSONReference(node: JSONReferenceNode): void;
    protected visitJSONPath(node: JSONPathNode): void;
    protected visitJSONPathLeg(node: JSONPathLegNode): void;
    protected visitJSONOperatorChain(node: JSONOperatorChainNode): void;
    protected visitMergeQuery(node: MergeQueryNode): void;
    protected visitMatched(node: MatchedNode): void;
    protected visitAddIndex(node: AddIndexNode): void;
    protected visitCast(node: CastNode): void;
    protected visitFetch(node: FetchNode): void;
    protected visitOutput(node: OutputNode): void;
    protected visitTop(node: TopNode): void;
    protected visitOrAction(node: OrActionNode): void;
    protected visitCollate(node: CollateNode): void;
    protected append(str: string): void;
    protected appendValue(parameter: unknown): void;
    protected getLeftIdentifierWrapper(): string;
    protected getRightIdentifierWrapper(): string;
    protected getCurrentParameterPlaceholder(): string;
    protected getLeftExplainOptionsWrapper(): string;
    protected getExplainOptionAssignment(): string;
    protected getExplainOptionsDelimiter(): string;
    protected getRightExplainOptionsWrapper(): string;
    protected sanitizeIdentifier(identifier: string): string;
    protected sanitizeStringLiteral(value: string): string;
    protected addParameter(parameter: unknown): void;
    protected appendImmediateValue(value: unknown): void;
    protected appendStringLiteral(value: string): void;
    protected sortSelectModifiers(arr: SelectModifierNode[]): ReadonlyArray<SelectModifierNode>;
    protected compileColumnAlterations(columnAlterations: readonly AlterTableColumnAlterationNode[]): void;
    /**
     * controls whether the dialect adds a "type" keyword before a column's new data
     * type in an ALTER TABLE statement.
     */
    protected announcesNewColumnDataType(): boolean;
}
