import { Driver } from '../driver/driver.js';
import { <PERSON>ys<PERSON> } from '../kysely.js';
import { QueryCompiler } from '../query-compiler/query-compiler.js';
import { DatabaseIntrospector } from './database-introspector.js';
import { DialectAdapter } from './dialect-adapter.js';
/**
 * A Dialect is the glue between Kysely and the underlying database engine.
 *
 * See the built-in {@link PostgresDialect} as an example of a dialect.
 * Users can implement their own dialects and use them by passing it
 * in the {@link KyselyConfig.dialect} property.
 */
export interface Dialect {
    /**
     * Creates a driver for the dialect.
     */
    createDriver(): Driver;
    /**
     * Creates a query compiler for the dialect.
     */
    createQueryCompiler(): QueryCompiler;
    /**
     * Creates an adapter for the dialect.
     */
    createAdapter(): DialectAdapter;
    /**
     * Creates a database introspector that can be used to get database metadata
     * such as the table names and column names of those tables.
     *
     * `db` never has any plugins installed. It's created using
     * {@link Kysely.withoutPlugins}.
     */
    createIntrospector(db: <PERSON><PERSON><PERSON><any>): DatabaseIntrospector;
}
