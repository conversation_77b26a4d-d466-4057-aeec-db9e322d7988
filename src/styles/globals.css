@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-strong {
    @apply bg-white/20 backdrop-blur-xl border border-white/30;
  }
  
  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent;
  }
  
  .gradient-text-primary {
    @apply bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent;
  }
  
  /* Animated gradient backgrounds */
  .gradient-bg-animated {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
  }
  
  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  /* Floating animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }
  
  /* Glow effects */
  .glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  
  .glow-pink {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
  }
  
  /* Input focus effects */
  .input-glow:focus {
    @apply ring-2 ring-blue-500/50 border-blue-500/50;
    box-shadow: 0 0 0 1px rgb(59 130 246 / 0.3), 0 0 20px rgb(59 130 246 / 0.2);
  }
  
  /* Button hover effects */
  .btn-gradient {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700;
    transition: all 0.3s ease;
  }
  
  .btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
  }
  
  /* Shimmer effect */
  .shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }
  
  /* Mesh gradient background */
  .mesh-bg {
    background: 
      radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 1) 0px, transparent 50%),
      radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 1) 0px, transparent 50%),
      radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 1) 0px, transparent 50%),
      radial-gradient(at 80% 50%, hsla(340, 100%, 76%, 1) 0px, transparent 50%),
      radial-gradient(at 0% 100%, hsla(22, 100%, 77%, 1) 0px, transparent 50%),
      radial-gradient(at 80% 100%, hsla(242, 100%, 70%, 1) 0px, transparent 50%),
      radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 1) 0px, transparent 50%);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Mobile optimizations */
  @media (max-width: 640px) {
    .glass {
      @apply bg-white/5 backdrop-blur-sm;
    }

    .glass-strong {
      @apply bg-white/10 backdrop-blur-md;
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .gradient-bg-animated {
      animation: none;
    }

    .float {
      animation: none;
    }

    .shimmer {
      animation: none;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .glass {
      @apply bg-white/20 border-white/40;
    }

    .glass-strong {
      @apply bg-white/30 border-white/50;
    }
  }
}
